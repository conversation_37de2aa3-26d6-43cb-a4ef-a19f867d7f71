package za.co.ipay.metermng.integration.bulkfreeissue;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.metermng.fileimport.FileImportHandler;
import za.co.ipay.metermng.fileimport.ImportItemDataConverter;
import za.co.ipay.metermng.fileimport.ImportParamRecordConverter;
import za.co.ipay.metermng.fileimport.exceptions.FileImportException;
import za.co.ipay.metermng.ipayxml.IpayXmlMessageService;
import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.server.mybatis.service.AppSettingService;
import za.co.ipay.metermng.server.mybatis.service.MeterService;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.GenericImportRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParamRecord;

/**
 * Import handler for bulk free issue operations.
 * Handles CSV file upload, validation, and token generation for multiple meters.
 */
public class BulkFreeIssueImportHandler implements FileImportHandler {

    private static final Logger logger = Logger.getLogger(BulkFreeIssueImportHandler.class);
    
    // App setting keys
    private static final String BULK_FREE_ISSUE_MAX_METERS = "bulk.free.issue.max.meters";
    private static final String BULK_FREE_ISSUE_MAX_UNITS = "bulk.free.issue.max.units";
    
    @Autowired
    private AppSettingService appSettingService;
    
    @Autowired
    private MeterService meterService;
    
    @Autowired
    private IpayXmlMessageService ipayXmlMessageService;

    @Override
    public void uploadFile(String username, Long userGenGroupId, ImportFile importFile, String folder, String filename) throws FileImportException {
        logger.info("BulkFreeIssueImportHandler: uploadFile() - " + filename);
        
        try {
            // Generate unique bulk reference for audit trail
            String bulkRef = generateBulkReference();
            importFile.setBulkRef(bulkRef);
            
            // Parse CSV file and create import items
            BulkFreeIssueCsvParser parser = new BulkFreeIssueCsvParser();
            List<BulkFreeIssueImportRecord> records = parser.parseFile(folder + "/" + filename);
            
            // Validate against app settings
            validateBulkLimits(records);
            
            // Create import file items
            for (BulkFreeIssueImportRecord record : records) {
                ImportFileItem item = new ImportFileItem();
                item.setImportFileId(importFile.getId());
                item.setItemData(convertRecordToString(record));
                item.setUploadDate(new Date());
                item.setUploadSuccessful(true);
                item.setLastImportSuccessful(false);
                item.setNumImportAttempts(0);
                
                // Save item to database (this would be done by the framework)
                // importFileItemMapper.insert(item);
            }
            
        } catch (Exception e) {
            logger.error("Error uploading bulk free issue file: " + filename, e);
            throw new FileImportException("Error processing bulk free issue file", e);
        }
    }

    @Override
    public void importFile(String username, Long userGenGroupId, Long userAccessGroupId, ImportFile importFile, List<ImportFileItem> itemsToImport, boolean isAccessGroupsEnabled) throws FileImportException {
        logger.info("BulkFreeIssueImportHandler: importFile() - processing " + itemsToImport.size() + " items");
        
        try {
            String bulkRef = importFile.getBulkRef();
            if (bulkRef == null || bulkRef.isEmpty()) {
                bulkRef = generateBulkReference();
                importFile.setBulkRef(bulkRef);
            }
            
            // Get bulk parameters if available
            BulkFreeIssueParamRecord bulkParams = getBulkParameters(importFile);
            
            for (ImportFileItem item : itemsToImport) {
                try {
                    // Parse the import record
                    BulkFreeIssueImportRecord record = convertStringToRecord(item.getItemData());
                    
                    // Apply bulk parameters if individual values are not set
                    applyBulkParameters(record, bulkParams);
                    
                    // Validate the record
                    validateRecord(record, userGenGroupId);
                    
                    // Generate free issue token
                    generateFreeIssueToken(record, username, bulkRef);
                    
                    // Mark as successful
                    item.setLastImportSuccessful(true);
                    item.setComment("Free issue token generated successfully");
                    
                } catch (Exception e) {
                    logger.error("Error processing item: " + item.getItemData(), e);
                    item.setLastImportSuccessful(false);
                    item.setComment("Error: " + e.getMessage());
                }
                
                item.setNumImportAttempts(item.getNumImportAttempts() + 1);
                item.setLastImportDate(new Date());
            }
            
        } catch (Exception e) {
            logger.error("Error importing bulk free issue file", e);
            throw new FileImportException("Error importing bulk free issue file", e);
        }
    }

    @Override
    public ImportItemDataConverter getImportItemDataConverter() {
        return new BulkFreeIssueItemDataConverter();
    }

    @Override
    public ImportParamRecordConverter getImportParamRecordConverter() {
        return new BulkFreeIssueParamRecordConverter();
    }

    @Override
    public boolean allowExportFailedItems() {
        return true;
    }

    @Override
    public boolean allowItemsMultipleImport() {
        return false; // Free issue tokens should not be generated multiple times
    }

    @Override
    public boolean hasActionParams() {
        return true; // Supports bulk parameters
    }

    // Private helper methods
    
    private String generateBulkReference() {
        return "BFI_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    private void validateBulkLimits(List<BulkFreeIssueImportRecord> records) throws ValidationException {
        // Check maximum number of meters
        Integer maxMeters = appSettingService.getAppSettingAsInteger(BULK_FREE_ISSUE_MAX_METERS);
        if (maxMeters != null && maxMeters > 0 && records.size() > maxMeters) {
            throw new ValidationException("bulk.free.issue.max.meters.exceeded", 
                new Object[]{records.size(), maxMeters});
        }
        
        // Check maximum units per meter
        Integer maxUnits = appSettingService.getAppSettingAsInteger(BULK_FREE_ISSUE_MAX_UNITS);
        if (maxUnits != null && maxUnits > 0) {
            for (BulkFreeIssueImportRecord record : records) {
                if (record.getUnits() != null && record.getUnits() > maxUnits) {
                    throw new ValidationException("bulk.free.issue.max.units.exceeded", 
                        new Object[]{record.getMeterNum(), record.getUnits(), maxUnits});
                }
            }
        }
    }
    
    private void validateRecord(BulkFreeIssueImportRecord record, Long userGenGroupId) throws ValidationException {
        // Validate meter number
        if (record.getMeterNum() == null || record.getMeterNum().trim().isEmpty()) {
            throw new ValidationException("bulk.free.issue.meter.number.required");
        }
        
        // Validate units
        if (record.getUnits() == null || record.getUnits() <= 0) {
            throw new ValidationException("bulk.free.issue.units.required.positive");
        }
        
        // Check if meter exists and is accessible
        if (!meterService.meterExistsAndAccessible(record.getMeterNum(), userGenGroupId)) {
            throw new ValidationException("bulk.free.issue.meter.not.found", 
                new Object[]{record.getMeterNum()});
        }
    }
    
    private BulkFreeIssueParamRecord getBulkParameters(ImportFile importFile) {
        if (importFile.getActionParams() != null && !importFile.getActionParams().isEmpty()) {
            try {
                return (BulkFreeIssueParamRecord) getImportParamRecordConverter()
                    .convertToObject(importFile.getActionParams());
            } catch (Exception e) {
                logger.warn("Could not parse bulk parameters", e);
            }
        }
        return null;
    }
    
    private void applyBulkParameters(BulkFreeIssueImportRecord record, BulkFreeIssueParamRecord bulkParams) {
        if (bulkParams == null) return;
        
        // Apply bulk parameters only if individual values are not set
        if (record.getUnits() == null && bulkParams.getUnits() != null) {
            record.setUnits(bulkParams.getUnits());
        }
        if ((record.getDescription() == null || record.getDescription().isEmpty()) && bulkParams.getDescription() != null) {
            record.setDescription(bulkParams.getDescription());
        }
        if ((record.getUserReference() == null || record.getUserReference().isEmpty()) && bulkParams.getUserReference() != null) {
            record.setUserReference(bulkParams.getUserReference());
        }
        if ((record.getReason() == null || record.getReason().isEmpty()) && bulkParams.getReasonText() != null) {
            record.setReason(bulkParams.getReasonText());
        }
    }
    
    private void generateFreeIssueToken(BulkFreeIssueImportRecord record, String username, String bulkRef) throws Exception {
        // Create and send stsEngTokenReq message
        // This would use the existing token generation infrastructure
        // Implementation details would depend on the existing IpayXmlMessageService
        
        logger.info("Generating free issue token for meter: " + record.getMeterNum() + 
                   ", units: " + record.getUnits() + ", bulkRef: " + bulkRef);
        
        // TODO: Implement actual token generation using ipayXmlMessageService
        // This would involve creating the XML message as shown in the requirements
        // and processing the response to update the StsEngineeringToken table
    }
    
    private String convertRecordToString(BulkFreeIssueImportRecord record) {
        // Convert record to CSV string format
        StringBuilder sb = new StringBuilder();
        sb.append(record.getMeterNum()).append(",");
        sb.append(record.getUnits() != null ? record.getUnits() : "").append(",");
        sb.append(record.getDescription() != null ? record.getDescription() : "").append(",");
        sb.append(record.getUserReference() != null ? record.getUserReference() : "").append(",");
        sb.append(record.getReason() != null ? record.getReason() : "").append(",");
        sb.append(record.getCustomReason() != null ? record.getCustomReason() : "");
        return sb.toString();
    }
    
    private BulkFreeIssueImportRecord convertStringToRecord(String itemData) {
        // Parse CSV string back to record
        String[] parts = itemData.split(",", -1);
        BulkFreeIssueImportRecord record = new BulkFreeIssueImportRecord();
        
        if (parts.length > 0) record.setMeterNum(parts[0].trim());
        if (parts.length > 1 && !parts[1].trim().isEmpty()) {
            record.setUnits(Integer.parseInt(parts[1].trim()));
        }
        if (parts.length > 2) record.setDescription(parts[2].trim());
        if (parts.length > 3) record.setUserReference(parts[3].trim());
        if (parts.length > 4) record.setReason(parts[4].trim());
        if (parts.length > 5) record.setCustomReason(parts[5].trim());
        
        return record;
    }
}
