package za.co.ipay.metermng.client.view.component.importfile;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.widgets.IpayIntegerBox;
import za.co.ipay.gwt.common.client.widgets.IpayTextBox;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.SpecialActionsReasonComponent;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;

public class BulkFreeIssueImportDialogueBox extends ImportFileItemBaseDialogueBox {

    @UiField FormElement meterNumElement;
    @UiField IpayTextBox meterNumTextBox;
    
    @UiField FormElement unitsElement;
    @UiField IpayIntegerBox unitsIntegerBox;
    
    @UiField FormElement descriptionElement;
    @UiField IpayTextBox descriptionTextBox;
    
    @UiField FormElement userReferenceElement;
    @UiField IpayTextBox userReferenceTextBox;
    
    @UiField FormElement reasonElement;
    @UiField SpecialActionsReasonComponent reasonComponent;

    private static BulkFreeIssueImportDialogueBoxUiBinder uiBinder = GWT.create(BulkFreeIssueImportDialogueBoxUiBinder.class);

    interface BulkFreeIssueImportDialogueBoxUiBinder extends UiBinder<Widget, BulkFreeIssueImportDialogueBox> {
    }

    public BulkFreeIssueImportDialogueBox(ClientFactory clientFactory, ImportFileItemView parent) {
        super(clientFactory, parent);
        setWidget(uiBinder.createAndBindUi(this));
        initHeaders();
        initValidation();
    }

    private void initHeaders() {
        meterNumElement.setLabelText(messagesInstance.getMessage("import.meter.label"));
        unitsElement.setLabelText(messagesInstance.getMessage("bulk.free.issue.units.label"));
        descriptionElement.setLabelText(messagesInstance.getMessage("bulk.free.issue.description.label"));
        userReferenceElement.setLabelText(messagesInstance.getMessage("bulk.free.issue.user.reference.label"));
        reasonElement.setLabelText(messagesInstance.getMessage("bulk.free.issue.reason.label"));
    }

    private void initValidation() {
        meterNumElement.setRequired(true);
        unitsElement.setRequired(true);
    }

    @Override
    protected void populateDialogueBox(ImportFileItemDto importFileItemDto) {
        BulkFreeIssueImportRecord record = importFileItemDto.getBulkFreeIssueImportRecord();
        
        meterNumTextBox.setValue(record.getMeterNum());
        unitsIntegerBox.setValue(record.getUnits());
        descriptionTextBox.setValue(record.getDescription());
        userReferenceTextBox.setValue(record.getUserReference());
        
        // Set reason component values
        reasonComponent.setReasonText(record.getReason());
        reasonComponent.setCustomReasonText(record.getCustomReason());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItemDto) {
        BulkFreeIssueImportRecord record = new BulkFreeIssueImportRecord();
        
        record.setMeterNum(meterNumTextBox.getValue());
        record.setUnits(unitsIntegerBox.getValue());
        record.setDescription(descriptionTextBox.getValue());
        record.setUserReference(userReferenceTextBox.getValue());
        record.setReason(reasonComponent.getReasonText());
        record.setCustomReason(reasonComponent.getCustomReasonText());
        
        importFileItemDto.setGenericImportRecord(record);
        importFileItemDto.getImportFileItem().setComment("");
        importFileItemDto.setImportFileItemImportComment("");
    }

    @Override
    protected boolean validateDialogueBox() {
        boolean isValid = true;
        
        // Clear previous error messages
        meterNumElement.clearErrorMsg();
        unitsElement.clearErrorMsg();
        
        // Validate meter number
        if (meterNumTextBox.getValue() == null || meterNumTextBox.getValue().trim().isEmpty()) {
            meterNumElement.showErrorMsg(messagesInstance.getMessage("validation.required"));
            isValid = false;
        }
        
        // Validate units
        if (unitsIntegerBox.getValue() == null || unitsIntegerBox.getValue() <= 0) {
            unitsElement.showErrorMsg(messagesInstance.getMessage("bulk.free.issue.units.validation.positive"));
            isValid = false;
        }
        
        return isValid;
    }
}
