package za.co.ipay.metermng.shared;

public enum ImportFileTypePermissionsE {
    MM_SAP_CUSTOMER_MOVEMENTS("za.co.ipay.metermng.integration.sap.customermovement.CustomerMovementFileImportHandler"),
    MM_SAP_DEBT_IMPORT("za.co.ipay.metermng.integration.sap.debtimport.DebtImportHandler"),
    MM_SOLAR_DEBT_IMPORT("za.co.ipay.metermng.integration.solar.debtimport.SolarDebtImportHandler"),
    MM_SAMRAS_DEBT_IMPORT("za.co.ipay.metermng.integration.samras.debtimport.SamrasDebtImportHandler"),
    MM_METER_CUST_UP_IMPORT(
            "za.co.ipay.metermng.integration.offlinebulkuploads.metercustup.MeterCustUpBulkImportHandler"),
    MM_REGISTER_READING_IMPORT("za.co.ipay.metermng.integration.registerreading.RegisterReadingImportHandler"),
    MM_IPAY_DEBT_IMPORT("za.co.ipay.metermng.integration.ipay.debtimport.IpayDebtImportHandler"),
    MM_SOL_PLAATJE_DEBT_IMPORT("za.co.ipay.metermng.integration.solplaatje.debtimport.SolPlaatjeDebtImportHandler"),
    MM_3E_DEBT_IMPORT("za.co.ipay.metermng.integration.threee.debtimport.ThreeEDebtImportHandler"),
    MM_RICHARDS_BAY_CUSTOMER_MOVEMENTS(
            "za.co.ipay.metermng.integration.richardsbay.customermovement.CustomerMovementFileImportHandler"),
    MM_BULK_KEYCHANGE("za.co.ipay.metermng.integration.bulkKeychange.BulkKeyChangeImportHandler"),
    MM_BULK_PRICING_STRUCTURE_CHANGE(
            "za.co.ipay.metermng.integration.bulkpricingstructurechange.BulkPricingStructureChangeImportHandler"),
    MM_PRICING_STRUCTURE_CHANGE_IMPORT(
            "za.co.ipay.metermng.integration.pricingstructurechange.PricingStructureChangeImportHandler"),
    MM_BULK_BLOCKING("za.co.ipay.metermng.integration.bulkblocking.BulkBlockingImportHandler"),
    MM_BULK_UNBLOCKING("za.co.ipay.metermng.integration.bulkblocking.BulkBlockingImportHandler"),
    MM_PRICING_STRUCT_IMPORT("za.co.ipay.metermng.integration.TariffBulkUpload.TariffPsImportHandler"),
    MM_BULK_STORE_MOVEMENT("za.co.ipay.metermng.integration.bulkstoremovement.BulkStoreMovementImportHandler"),
    MM_BULK_MDC_MESSAGE_IMPORT("za.co.ipay.metermng.integration.bulkmdccontrol.BulkMdcImportHandler");

    private String typeClass;

    private ImportFileTypePermissionsE(String typeClass) {
        this.typeClass = typeClass;
    }

    public static ImportFileTypePermissionsE fromTypeClass(String typeClass) {
        for (ImportFileTypePermissionsE importFileTypePermissionsE : values()) {
            if (importFileTypePermissionsE.typeClass.equals(typeClass)) {
                return importFileTypePermissionsE;
            }
        }
        return null;
    }

    public String getTypeClass() {
        return typeClass;
    }
}